// import { View } from '@klook/admin-layout'
import { addDevTestRoutes } from './devRoutes'
import contentApiRouter from '../../contentApi/routes/index'
import revenueRouter from '../../revenue/routes/index'
import webAdminRouter from '../../web-admin/routes/index'
import solutionRouter from '@/modules/digitalSolution/routes/index'
import supplyApiRouter from '@/modules/supplyApi/routes/index'
import { TOURS_MENU_KEY_DICT, ATTRACTION_MENU_KEY_DICT } from '~src/modules/aidRevamp/utils/const.js'

// 如果URL package id 值是 SPU ID 会强制跳转到 aid 页面
// 若不需要跳转，则添加到白名单
const AID_ROUTE_NAME_WHITE_LIST = {
  itineraryAudioUpload: 'itineraryAudioUpload'
}

import store from '../store/index'
import Vuex from 'vuex'
import Vue from 'vue'
Vue.use(Vuex)
const storeInstance = new Vuex.Store(store)
const router = {
  base: '/',
  routes: [
    {
      path: 'act',
      meta: {
        title: 'Activity Manager'
      },
      children: [
        {
          path: '',
          component: () => import('../pages/index'),
          redirect: 'management',
          // props: route => ({
          //   template_id: store.state.categoryInfo.template_id,
          //   activity_id: +route.params.id,
          // }),
          async beforeEnter(to, from, next) {
            let activity_id = to.params.id

            if (activity_id) {
              let lockStatus = await ajax.get({
                url: ADMIN_API.act.edit_protect,
                params: {
                  activity_id
                }
              })

              if (lockStatus === false) {
                location.href = `${location.origin}/act/activity/list`
                return
              }
              // 检查 lockStatus , has_migrated
              const need_redirect = lockStatus && typeof lockStatus === 'object' && lockStatus.has_migrated
              const pid = Number(to.query.package_id)
              const data = await storeInstance.dispatch('getActCategory2action', {
                activity_id,
                page_from: to.query.page_from,
                refresh: true
              })

              const spuIds = data?.spu_id_list || []
              if (
                (!isNaN(pid) &&
                  pid &&
                  spuIds.includes(pid) &&
                  ![AID_ROUTE_NAME_WHITE_LIST.itineraryAudioUpload].includes(to.name)) ||
                need_redirect
              ) {
                // 统一处理 package_id 参数
                const getValidQueryParams = () => {
                  let queryParams = { ...to.query }
                  if (need_redirect) {
                    const currentPid = Number(to.query.package_id)
                    if (!currentPid || !spuIds.includes(currentPid)) {
                      // 如果没有 package_id 或者不在 spu_id_list 中，使用第一个 spu_id
                      if (spuIds.length > 0) {
                        queryParams.package_id = spuIds[0]
                      }
                    }
                  }
                  return queryParams
                }

                if (data.sub_category_id === 2) {
                  // Tours
                  next({
                    name: TOURS_MENU_KEY_DICT.basic.page,
                    params: to.params,
                    query: getValidQueryParams()
                  })
                  return
                }

                if ([1, 415].includes(data.sub_category_id)) {
                  // Attraction
                  next({
                    name: ATTRACTION_MENU_KEY_DICT.spuBasicInfo.page,
                    params: to.params,
                    query: getValidQueryParams()
                  })
                  return
                }
              }
            }

            next()
          },
          name: 'activity',
          meta: {
            title: 'Activity'
          },
          children: [
            {
              path: 'poi/tool',
              name: 'poiTool',
              component: () => import(/* webpackChunkName: "poiTool" */ '../pages/poiTool/index'),
              meta: {
                title: 'POI Tool',
                auth: ['experience/act/poi_tool_view', 'experience/act/poi_tool_edit']
              }
            },
            {
              path: 'rate/plan',
              name: 'retePlan',
              component: () => import('../pages/ratePlan/index'),
              meta: {
                title: false
              },
              redirect: 'rate/plan/list',
              children: [
                {
                  path: 'create',
                  name: 'createRatePlan',
                  component: () => import('../pages/ratePlan/create'),
                  meta: {
                    title: 'Create rate plan',
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                {
                  path: 'list',
                  name: 'ratePlanList',
                  component: () => import('../pages/ratePlan/list'),
                  meta: {
                    title: 'All Rate Plans',
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                }
              ]
            },
            {
              path: 'ticket/:ticket_id/detail',
              name: 'ticketDetail',
              component: () =>
                import(
                  /* webpackChunkName: "ticketDetail" */ '../pages/activityManagement/approveList/ticket/detail'
                ),
              hidden: true,
              meta: {
                title: 'Ticket Detail',
                auth: ['experience/act/activity_view', 'experience/act/activity_edit']
              }
            },
            {
              path: 'default',
              name: 'activityDefault',
              component: () =>
                import(/* webpackChunkName: "activityDefault" */ '../pages/activityManagement/default')
            },
            {
              path: 'activity/add',
              name: 'activityAdd',
              component: () =>
                import(/* webpackChunkName: "activityAdd" */ '../pages/activityManagement/add'),
              hidden: true,
              meta: {
                title: 'New Activity',
                auth: ['experience/act/activity_view', 'experience/act/activity_edit']
              }
            },
            {
              path: 'management',
              name: 'activityManagement',
              component: () =>
                import(/* webpackChunkName: "activityManagement" */ '../pages/activityManagement'),
              meta: {
                title: 'Approval List-Merchant',
                auth: ['experience/act/merchant/list_page']
              }
            },
            {
              path: 'poi/management',
              name: 'poiManagement',
              component: () => import(/* webpackChunkName: "poiManagement" */ '../pages/poiManagement'),
              meta: {
                title: 'T&A POI page management',
                auth: ['experience/poi_page/view', 'experience/poi_page/edit']
              }
            },
            {
              path: 'general_management',
              name: 'generalManagement',
              hidden: true,
              component: () =>
                import(/* webpackChunkName: "generalManagement" */ '../pages/generalManagement'),
              meta: {
                title: 'General Info Mgmt Tool',
                auth: ['experience/act/subcategory_config_view', 'experience/act/subcategory_config_operate']
              }
            },
            {
              path: 'activity_checkout',
              name: 'activityCheckout',
              hidden: true,
              component: () =>
                import(/* webpackChunkName: "activityCheckout" */ '../pages/activityCheckout/checkoutList'),
              meta: {
                title: 'Express Check-out Activity List',
                auth: ['experience/express_checkout_activity_operator'],
                keepAlive: true
              }
            },
            {
              path: 'pkg_publish_limit',
              name: 'pkgPublishLimit',
              hidden: true,
              component: () =>
                import(/* webpackChunkName: "pkgPublishLimit" */ '../pages/packagePublishSetting/index'),
              meta: {
                title: 'Package publish restriction setting',
                auth: []
              }
            },
            {
              hidden: true,
              path: 'checkout_edit',
              name: 'checkoutEdit',
              component: () =>
                import(/* webpackChunkName: "checkoutEdit" */ '../pages/activityCheckout/checkoutDetail'),
              meta: {
                title: 'Express Check-out Activity Edit',
                setLeftNav: true,
                auth: ['experience/express_checkout_activity_operator']
              }
            },
            {
              hidden: true,
              path: 'clear_cache',
              name: 'clearCache',
              component: () =>
                import(/* webpackChunkName: "clearCache" */ '../pages/clearActivityCache/index'),
              meta: {
                title: 'Clear Cache',
                setLeftNav: true,
                auth: []
              }
            },
            {
              hidden: true,
              path: 'redirection',
              name: 'Redirection',
              component: () =>
                import(/* webpackChunkName: "redirection" */ '../pages/activityRedirection/index'),
              meta: {
                title: 'Seo Setting',
                setLeftNav: true,
                auth: []
              }
            },
            {
              hidden: true,
              path: 'activity',
              name: 'actLayout',
              component: () => import('../layout/actLayout'),
              meta: {
                title: false,
                setLeftNav: false
              },
              children: [
                {
                  path: 'basic/:id',
                  name: 'basicInfo',
                  component: () =>
                    import(/* webpackChunkName: "basicInfo" */ '../pages/activityManagement/basicInfo'),
                  meta: {
                    title: false,
                    hideSummary: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                // {
                //   path: 'detail/:id',
                //   name: 'detail',
                //   component: () =>
                //     import(/* webpackChunkName: "activityDetail" */ '../pages/activityManagement/detail'),
                //   meta: {
                //     auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                //   }
                // },
                {
                  path: 'detail/:id',
                  name: 'detail',
                  component: () =>
                    import(/* webpackChunkName: "activityDetail" */ '../pages/activityManagement/detailV2'),
                  meta: {
                    title: false,
                    hideSummary: true,
                    hideTimeline: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                // {
                //   path: 'tags/:id',
                //   name: 'tags',
                //   component: () => import('../pages/activityManagement/tags'),
                //   meta: {
                //     title: false,
                //     hideSummary: true,
                //     hideTimeline: true
                //   }
                // },
                {
                  path: 'notes/:id',
                  name: 'notes',
                  component: () =>
                    import(/* webpackChunkName: "activityNotes" */ '../pages/activityManagement/notes'),
                  meta: {
                    hideSummary: true,
                    hideTimeline: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                {
                  path: 'tags/:id',
                  name: 'tags',
                  component: () =>
                    import(/* webpackChunkName: "activityTags" */ '../pages/activityManagement/specialStep'),
                  meta: {
                    hideSummary: true,
                    hideTimeline: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                {
                  path: 'vertical/:id',
                  name: 'vertical',
                  component: () =>
                    import(
                      /* webpackChunkName: "activityVertical" */ '../pages/activityManagement/specialStep'
                    ),
                  meta: {
                    hideSummary: true,
                    hideTimeline: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                {
                  path: 'special/:id',
                  name: 'special',
                  component: () =>
                    import(
                      /* webpackChunkName: "activitySpecial" */ '../pages/activityManagement/specialStep'
                    ),
                  meta: {
                    hideSummary: true,
                    hideTimeline: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                {
                  path: 'seo/:id',
                  name: 'seo',
                  component: () =>
                    import(
                      /* webpackChunkName: "activitySeo" */ '../pages/components/content/content_create'
                    ),
                  meta: {
                    hideSummary: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                {
                  path: 'addon/:id',
                  name: 'activityAddon',
                  component: () =>
                    import(/* webpackChunkName: "activityAddon" */ '../pages/activityManagement/addon/index'),
                  meta: {
                    title: false,
                    hideSummary: true,
                    hideTimeline: true,
                    hideTopInfo: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                },
                {
                  path: 'sort/:id',
                  name: 'activitySort',
                  component: () =>
                    import(/* webpackChunkName: "activitySort" */ '../pages/activityManagement/sort/index'),
                  meta: {
                    title: false,
                    hideSummary: true,
                    hideTimeline: true,
                    hideTopInfo: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  }
                }
              ]
            },
            {
              hidden: true,
              path: 'activity/grouping/:activity_id',
              name: 'activityGrouping',
              component: () => import(/* webpackChunkName: "activityGrouping" */ '../pages/activityGrouping'),
              meta: {
                title: 'Create Group',
                auth: ['act_group_editor']
              }
            },
            {
              hidden: true,
              path: 'activity/content-qa',
              name: 'activity-ContentQa',
              component: () =>
                import(/* webpackChunkName: "activityGrouping" */ '../pages/activityContentQA'),
              meta: {
                title: 'Content Qa'
              }
            },
            {
              path: 'itineraryAudioUpload/:id',
              name: AID_ROUTE_NAME_WHITE_LIST.itineraryAudioUpload,
              component: () =>
                import(
                  /* webpackChunkName: "itineraryAudioUpload" */ '../pages/package/itinerary-audio-upload/index'
                ),
              meta: {
                title: 'Audio Guide Upload'
              },
              props: (route) => ({
                // pass props obj to router-view child component
                package_id: +route.query.package_id,
                activity_id: +route.params.id
              })
            },
            {
              hidden: true,
              path: 'package',
              name: 'packageInfo',
              component: () => import('../layout/actLayout'),
              meta: {
                title: false,
                setLeftNav: false
              },
              children: [
                {
                  path: 'info/:id',
                  name: 'packageBasicInfo',
                  component: () =>
                    import(/* webpackChunkName: "packageBasicInfo" */ '../pages/package/basicInfo/index'),
                  meta: {
                    title: false,
                    hideSummary: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                },
                {
                  path: 'itinerary/:id',
                  name: 'packageItinerary',
                  component: () =>
                    import(/* webpackChunkName: "packageItinerary" */ '../pages/package/itinerary/index'),
                  meta: {
                    hideSummary: true,
                    hideTimeline: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                },
                {
                  path: 'detail/:id',
                  name: 'packageDetail',
                  component: () => import(/* webpackChunkName: "packageDetail" */ '../pages/package/detail'),
                  meta: {
                    title: false,
                    hideSummary: true,
                    hideTimeline: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                },
                {
                  path: 'special/:id',
                  name: 'pkgSpecial',
                  component: () =>
                    import(/* webpackChunkName: "pkgSpecial" */ '../pages/activityManagement/specialStep'),
                  meta: {
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit'],
                    hideSummary: true,
                    hideTimeline: true,
                    hideSave: true
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                },
                {
                  path: 'schedule/:id',
                  name: 'packageSchedule',
                  component: () => import(/* webpackChunkName: "packageSchedule" */ '../pages/package/units'),
                  meta: {
                    hideTimeline: true,
                    hideSave: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                },
                {
                  path: 'mapping/:id',
                  name: 'packageSupplyApiMapping',
                  component: () =>
                    import(
                      /* webpackChunkName: "packageSupplyApiMapping" */ '../pages/package/supply-api-mapping/index.vue'
                    ),
                  meta: {
                    hideSave: false,
                    hideSummary: true,
                    hideTimeline: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                },
                {
                  path: 'extra/:id',
                  name: 'packageExtra',
                  component: () => import(/* webpackChunkName: "packageExtra" */ '../pages/package/extra'),
                  meta: {
                    hideSave: true,
                    hideSummary: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                },
                {
                  path: 'financial_model/:id',
                  name: 'financial_model',
                  component: () =>
                    import(/* webpackChunkName: "financial_model" */ '../pages/package/financial_model'),
                  meta: {
                    hideSave: true,
                    hideTimeline: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                },
                {
                  path: 'bulk/:id',
                  name: 'packageBulkEdit',
                  component: () =>
                    import(/* webpackChunkName: "packageBulkEdit" */ '../pages/package/units/bulkEdit'),
                  meta: {
                    hideSave: true,
                    hideSummary: true,
                    hideTimeline: true,
                    auth: ['experience/act/activity_view', 'experience/act/activity_edit']
                  },
                  props: (route) => ({
                    // pass props obj to router-view child component
                    package_id: +route.query.package_id,
                    activity_id: +route.params.id,
                    package_type: +route.query.package_type
                  })
                }
              ]
            }
          ]
        },
        {
          path: 'exportData',
          name: 'exportData',
          component: () => import(/* webpackChunkName: "exportData" */ '../pages/exportData/index.vue'),
          meta: {
            title: 'exportData',
            auth: ['experience/act/pass_batchupdate_edit']
          }
        },
        {
          path: 'klookpassoperationcenter/batchupdate',
          name: 'batchupdate',
          component: () => import(/* webpackChunkName: "exportData" */ '../pages/passBatchUpdate/index.vue'),
          meta: {
            title: 'passBatchUpdate',
            auth: ['experience/pass/batchupdateEdit']
          }
        },
        {
          path: 'guidance/video/management',
          name: 'GuidanceVideo',
          component: () =>
            import(/* webpackChunkName: "GuidanceVideo" */ '../pages/guidance-video-management/index'),
          meta: {
            title: 'Guidance Video'
          }
        },
        ...contentApiRouter
      ]
    },
    ...revenueRouter,
    ...solutionRouter,
    ...webAdminRouter,
    ...supplyApiRouter
  ]
}

// 添加dev环境测试路由
addDevTestRoutes(router)

export default router
